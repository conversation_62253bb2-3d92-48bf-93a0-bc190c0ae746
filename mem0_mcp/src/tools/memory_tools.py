"""
Memory management tools for Mem0 MCP server
"""

import json
from typing import Any, Dict, List, Optional

from .base_tool import <PERSON>T<PERSON>, ToolResult
from ..client.mem0_client import Mem0HT<PERSON><PERSON>lient
from ..client.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseAdapter
from ..config.settings import <PERSON>PConfig
from ..protocol.jsonrpc import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSONRPCResponse
from ..protocol.message_types import ToolsCallResponse
from ..utils.errors import ToolExecutionError
from ..utils.validators import (
    validate_memory_params,
    validate_search_params,
    validate_memory_id,
    validate_batch_delete_params,
    validate_required_fields
)
from ..utils.logger import get_logger

# Import graph tools  
from .graph_tools import GraphEntityTool, GraphRelationshipTool

# Import advanced memory management tools
from .advanced_tools import SelectiveMemoryTool, CriteriaRetrievalTool

logger = get_logger(__name__)


class AddMemoryTool(BaseTool):
    """Tool for adding memories from messages"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="add_memory",
            description="Add new memories from messages"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute add_memory tool"""
        try:
            await self.validate_arguments(arguments)
            
            # Extract required parameters
            messages = arguments["messages"]
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Optional parameters
            metadata = arguments.get("metadata", {})
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            if metadata:
                params["metadata"] = metadata
            
            # Add new advanced parameters (all optional for backward compatibility)
            advanced_params = [
                "custom_categories", "custom_instructions", "timestamp", 
                "version", "enable_graph", "includes", "excludes", "output_format"
            ]
            
            for param in advanced_params:
                if param in arguments and arguments[param] is not None:
                    params[param] = arguments[param]
            
            # Parameter validation for advanced features
            if "timestamp" in params:
                self._validate_timestamp(params["timestamp"])
            
            self.logger.debug(f"Adding memory for identity: {identity.get_primary_id()}")
            if any(param in params for param in advanced_params):
                self.logger.debug(f"Using advanced parameters: {[p for p in advanced_params if p in params]}")
            
            # Call Mem0 API
            response = await self.adapter.add_memory(messages, **params)
            
            # Debug: Print response type and content
            self.logger.debug(f"add_memory response type: {type(response)}")
            self.logger.debug(f"add_memory response: {response}")
            
            # Handle different response formats
            if isinstance(response, list):
                # Response is a list, likely from newer API version
                if len(response) > 0:
                    memory = response[0]
                    result_text = f"Successfully added memory"
                    if isinstance(memory, dict):
                        if "id" in memory:
                            result_text += f" with ID: {memory['id']}"
                        if "memory" in memory or "text" in memory:
                            content = memory.get("memory") or memory.get("text", "")
                            result_text += f"\nMemory content: {content}"
                        
                        # Add advanced feature results
                        if params.get("enable_graph") and "relations" in memory:
                            relations = memory["relations"]
                            if relations:
                                result_text += f"\nGraph relations extracted: {len(relations)} relations"
                        
                        if params.get("custom_categories") and "categories" in memory:
                            categories = memory["categories"]
                            if categories:
                                result_text += f"\nCategories: {', '.join(categories)}"
                    
                    # Build enhanced content response
                    content = [
                        {"type": "text", "text": result_text}
                    ]
                    
                    # Add advanced feature metadata if present
                    if any(param in params for param in ["enable_graph", "custom_categories", "timestamp", "version"]):
                        advanced_info = []
                        if params.get("enable_graph"):
                            advanced_info.append("Graph processing enabled")
                        if params.get("custom_categories"):
                            advanced_info.append(f"Custom categories: {len(params['custom_categories'])} rules")
                        if params.get("timestamp"):
                            advanced_info.append(f"Custom timestamp: {params['timestamp']}")
                        if params.get("version"):
                            advanced_info.append(f"API version: {params['version']}")
                        
                        if advanced_info:
                            content.append({
                                "type": "text", 
                                "text": f"Advanced features used: {', '.join(advanced_info)}"
                            })
                    
                    content.append({"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"})
                    return ToolResult(content=content)
                else:
                    return ToolResult.error("Empty response from Mem0 API")
            
            # Format response for MCP (original dict format)
            elif isinstance(response, dict) and response.get("success"):
                result_text = f"Successfully added memory"
                if "results" in response and response["results"]:
                    memories = response["results"]
                    if isinstance(memories, list) and len(memories) > 0:
                        memory = memories[0]
                        if "id" in memory:
                            result_text += f" with ID: {memory['id']}"
                        if "memory" in memory:
                            result_text += f"\nMemory content: {memory['memory']}"
                        
                        # Add advanced feature results
                        if params.get("enable_graph") and "relations" in memory:
                            relations = memory["relations"]
                            if relations:
                                result_text += f"\nGraph relations extracted: {len(relations)} relations"
                
                # Build enhanced content response  
                content = [
                    {"type": "text", "text": result_text}
                ]
                
                # Add advanced feature metadata if present
                if any(param in params for param in ["enable_graph", "custom_categories", "timestamp", "version"]):
                    advanced_info = []
                    if params.get("enable_graph"):
                        advanced_info.append("Graph processing enabled")
                    if params.get("custom_categories"):
                        advanced_info.append(f"Custom categories: {len(params['custom_categories'])} rules")
                    if params.get("timestamp"):
                        advanced_info.append(f"Custom timestamp: {params['timestamp']}")
                    if params.get("version"):
                        advanced_info.append(f"API version: {params['version']}")
                    
                    if advanced_info:
                        content.append({
                            "type": "text", 
                            "text": f"Advanced features used: {', '.join(advanced_info)}"
                        })
                
                content.append({"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"})
                return ToolResult(content=content)
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to add memory: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error in add_memory: {str(e)}")
            return ToolResult.error(str(e))
    
    def _validate_timestamp(self, timestamp: int):
        """Validate timestamp format
        
        Args:
            timestamp: Unix timestamp to validate
            
        Raises:
            ToolExecutionError: If timestamp is invalid
        """
        if not isinstance(timestamp, int) or timestamp < 0:
            raise ToolExecutionError("timestamp must be a non-negative integer", "parameter_validation")
        
        # Basic timestamp validation - should be reasonable Unix timestamp
        # Check if it's seconds (10 digits) or milliseconds (13 digits)
        timestamp_str = str(timestamp)
        if len(timestamp_str) != 10 and len(timestamp_str) != 13:
            raise ToolExecutionError(
                "timestamp should be Unix timestamp in seconds (10 digits) or milliseconds (13 digits)", 
                "parameter_validation"
            )
        
        # Check if timestamp is not too far in the future (more than 1 year)
        import time
        current_time = time.time()
        
        # Convert milliseconds to seconds if needed
        timestamp_seconds = timestamp if len(timestamp_str) == 10 else timestamp / 1000
        
        if timestamp_seconds > current_time + (365 * 24 * 60 * 60):  # More than 1 year in future
            raise ToolExecutionError(
                "timestamp cannot be more than 1 year in the future", 
                "parameter_validation"
            )
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for add_memory"""
        from ..protocol.message_types import ADD_MEMORY_SCHEMA
        return ADD_MEMORY_SCHEMA


class SearchMemoriesTool(BaseTool):
    """Tool for searching memories"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="search_memories",
            description="Search memories using natural language query. Supports graph relations when enable_graph=true"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute search_memories tool"""
        try:
            await self.validate_arguments(arguments)
            
            # Extract required parameters
            query = arguments["query"]
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Optional parameters
            limit = arguments.get("limit")
            filters = arguments.get("filters", {})
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            if limit:
                params["limit"] = limit
            if filters:
                params["filters"] = filters
            
            # Add advanced retrieval parameters (all optional for backward compatibility)
            advanced_params = [
                "keyword_search", "rerank", "filter_memories", "enable_graph",
                "semantic_weight", "keyword_weight", "enable_reranking", 
                "enable_filtering", "enable_selective_memory", "output_format"
            ]
            
            for param in advanced_params:
                if param in arguments and arguments[param] is not None:
                    params[param] = arguments[param]
            
            # Ensure output_format is set to v1.1 when graph is enabled for proper relations format
            if params.get("enable_graph") and "output_format" not in params:
                params["output_format"] = "v1.1"
            
            # Parameter validation for advanced features
            if "semantic_weight" in params and "keyword_weight" in params:
                self._validate_weights(params["semantic_weight"], params["keyword_weight"])
            
            self.logger.debug(f"Searching memories for identity: {identity.get_primary_id()}")
            if any(param in params for param in advanced_params):
                self.logger.debug(f"Using advanced retrieval: {[p for p in advanced_params if p in params]}")
            
            # Call Mem0 API
            response = await self.adapter.search_memories(query, **params)
            
            # Debug: Print response type and content
            self.logger.debug(f"search_memories response type: {type(response)}")
            self.logger.debug(f"search_memories response: {response}")
            
            # Handle different response formats and extract graph relations
            if isinstance(response, list):
                # Response is a list, likely from newer API version
                memories = response
                graph_relations = []
            elif isinstance(response, dict):
                # Check for V1 API format with results and relations
                if "results" in response:
                    memories = response.get("results", [])
                    # Handle both 'relations' and 'destination' field formats
                    graph_relations = response.get("relations", [])
                    # Normalize relation format to handle both 'target' and 'destination' fields
                    for relation in graph_relations:
                        if "destination" in relation and "target" not in relation:
                            relation["target"] = relation["destination"]
                elif response.get("success") or isinstance(response.get("data"), list):
                    memories = response.get("data", [])
                    graph_relations = response.get("relations", [])
                else:
                    memories = [response] if "id" in response else []
                    graph_relations = []
            else:
                return ToolResult.error(f"Unexpected response format: {type(response)}")
                
            if not memories:
                return ToolResult.success("No memories found matching the query.")
                
            # Format memories for display with advanced retrieval info
            result_lines = [f"Found {len(memories)} memories"]
            
            # Add search metadata for advanced features
            search_metadata = []
            if params.get("keyword_search"):
                search_metadata.append("BM25 keyword search")
            if params.get("rerank") or params.get("enable_reranking"):
                search_metadata.append("LLM reranking")
            if params.get("filter_memories") or params.get("enable_filtering"):
                search_metadata.append("intelligent filtering")
            if params.get("enable_graph"):
                search_metadata.append("graph search")
            if params.get("semantic_weight") is not None or params.get("keyword_weight") is not None:
                sw = params.get("semantic_weight", 0.7)
                kw = params.get("keyword_weight", 0.3)
                search_metadata.append(f"hybrid search (semantic:{sw}, keyword:{kw})")
            
            if search_metadata:
                result_lines[0] += f" using {', '.join(search_metadata)}"
            result_lines[0] += ":"
            
            # Display memory results with enhanced information
            for i, memory in enumerate(memories[:10], 1):  # Limit to first 10
                memory_id = memory.get("id", "unknown")
                memory_content = memory.get("memory", "")
                score = memory.get("score", "N/A")
                
                result_lines.append(f"\n{i}. ID: {memory_id}")
                result_lines.append(f"   Score: {score}")
                result_lines.append(f"   Content: {memory_content}")
                
                # Add category info if available
                if "categories" in memory and memory["categories"]:
                    categories = memory["categories"][:3]  # Show first 3 categories
                    result_lines.append(f"   Categories: {', '.join(categories)}")
            
            if len(memories) > 10:
                result_lines.append(f"\n... and {len(memories) - 10} more memories")
            
            # Add graph relations summary and details if available
            if graph_relations and params.get("enable_graph"):
                result_lines.append(f"\nGraph Relations Found: {len(graph_relations)} total relations")
                
                # Generate Mermaid diagram
                mermaid_diagram = self._generate_mermaid_diagram(graph_relations)
                result_lines.append("\nMermaid Graph Diagram:")
                result_lines.append("```mermaid")
                result_lines.append(mermaid_diagram)
                result_lines.append("```")
                
                result_lines.append("\nGraph Relationships:")
                for i, relation in enumerate(graph_relations[:5], 1):  # Show first 5 relations
                    source = relation.get("source", "unknown")
                    target = relation.get("target") or relation.get("destination", "unknown")
                    rel_type = relation.get("relationship", "related")
                    
                    result_lines.append(f"  {i}. {source} -{rel_type}-> {target}")
                
                if len(graph_relations) > 5:
                    result_lines.append(f"  ... and {len(graph_relations) - 5} more relations")
                
            # Build content response
            content = [
                {"type": "text", "text": "\n".join(result_lines)}
            ]
            
            # Add advanced retrieval metadata
            if any(param in params for param in ["keyword_search", "rerank", "filter_memories", "enable_graph"]):
                metadata_info = []
                if params.get("keyword_search"):
                    metadata_info.append("BM25 keyword search enabled")
                if params.get("rerank") or params.get("enable_reranking"):
                    metadata_info.append("LLM reranking applied")
                if params.get("filter_memories") or params.get("enable_filtering"):
                    metadata_info.append("Intelligent filtering applied")
                if params.get("enable_graph"):
                    graph_count = len(graph_relations) if graph_relations else 0
                    metadata_info.append(f"Graph search enabled ({graph_count} relations)")
                
                if metadata_info:
                    content.append({
                        "type": "text",
                        "text": f"Advanced retrieval features: {', '.join(metadata_info)}"
                    })
            
            content.append({"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"})
            return ToolResult(content=content)
                
        except Exception as e:
            self.logger.error(f"Error in search_memories: {str(e)}")
            return ToolResult.error(str(e))
    
    def _validate_weights(self, semantic_weight: float, keyword_weight: float):
        """Validate semantic and keyword weights
        
        Args:
            semantic_weight: Weight for semantic search (0.0-1.0)
            keyword_weight: Weight for keyword search (0.0-1.0)
            
        Raises:
            ToolExecutionError: If weights are invalid
        """
        if not (0.0 <= semantic_weight <= 1.0):
            raise ToolExecutionError(
                "semantic_weight must be between 0.0 and 1.0",
                "parameter_validation"
            )
            
        if not (0.0 <= keyword_weight <= 1.0):
            raise ToolExecutionError(
                "keyword_weight must be between 0.0 and 1.0", 
                "parameter_validation"
            )
        
        # Weights should approximately sum to 1.0 for proper hybrid search
        total_weight = semantic_weight + keyword_weight
        if not (0.8 <= total_weight <= 1.2):  # Allow some tolerance
            self.logger.warning(
                f"Weights sum to {total_weight:.2f}. Recommended: semantic + keyword ≈ 1.0"
            )
    
    def _generate_mermaid_diagram(self, graph_relations: List[Dict[str, Any]]) -> str:
        """生成Mermaid图表格式"""
        lines = ["graph LR"]
        
        # 处理关系，生成Mermaid语法
        for relation in graph_relations:
            source = relation.get("source", "unknown")
            target = relation.get("target") or relation.get("destination", "unknown")
            rel_type = relation.get("relationship", "related")
            
            # 清理节点名称，移除特殊字符
            clean_source = self._clean_mermaid_name(source)
            clean_target = self._clean_mermaid_name(target)
            
            # 生成Mermaid节点和连线语法
            lines.append(f"  {clean_source}[{source}] -->|{rel_type}| {clean_target}[{target}]")
        
        return "\n".join(lines)
    
    def _clean_mermaid_name(self, name: str) -> str:
        """清理Mermaid节点名称，移除特殊字符"""
        # 移除或替换特殊字符，确保Mermaid兼容
        import re
        # 替换特殊字符为下划线
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        # 确保以字母开头
        if cleaned and not cleaned[0].isalpha():
            cleaned = 'node_' + cleaned
        return cleaned or 'unknown_node'
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for search_memories"""
        from ..protocol.message_types import SEARCH_MEMORIES_SCHEMA
        return SEARCH_MEMORIES_SCHEMA


class GetMemoriesTool(BaseTool):
    """Tool for getting memories"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="get_memories",
            description="Get memories for a user, agent, or run"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute get_memories tool"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            # Add any additional arguments (like limit, filters, etc.)
            for key, value in arguments.items():
                if key not in ["user_id", "agent_id", "run_id"] and value is not None:
                    params[key] = value
            
            self.logger.debug(f"Getting memories for identity: {identity.get_primary_id()}")
            
            # Call Mem0 API
            response = await self.adapter.get_memories(**params)
            
            # Debug: Print response type and content
            self.logger.debug(f"get_memories response type: {type(response)}")
            self.logger.debug(f"get_memories response: {response}")
            
            # Handle direct API response format (based on direct testing)
            if isinstance(response, dict) and "results" in response:
                # V1 API returns {"results": [...], "relations": [...]}
                memories = response["results"]
            elif isinstance(response, list):
                # Direct list of memories
                memories = response
            else:
                return ToolResult.error(f"Failed to get memories: Unknown error occurred")
                
            if not memories:
                return ToolResult.success("No memories found.")
                
            # Format memories for display
            result_lines = [f"Found {len(memories)} memories:"]
            
            for i, memory in enumerate(memories[:10], 1):  # Limit to first 10
                memory_id = memory.get("id", "unknown")
                memory_content = memory.get("memory", "")
                created_at = memory.get("created_at", "unknown")
                
                result_lines.append(f"\n{i}. ID: {memory_id}")
                result_lines.append(f"   Created: {created_at}")
                result_lines.append(f"   Content: {memory_content}")
                
                if len(memories) > 10:
                    result_lines.append(f"\n... and {len(memories) - 10} more memories")
                
                content = [
                    {"type": "text", "text": "\n".join(result_lines)},
                    {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
                ]
                return ToolResult(content=content)
            else:
                error_msg = response.get("error", "Unknown error occurred")
                return ToolResult.error(f"Failed to get memories: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error in get_memories: {str(e)}")
            return ToolResult.error(str(e))
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for get_memories"""
        from ..protocol.message_types import GET_MEMORIES_SCHEMA
        return GET_MEMORIES_SCHEMA


class GetMemoryByIdTool(BaseTool):
    """Tool for getting a specific memory by ID"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="get_memory_by_id",
            description="Get a specific memory by its ID"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute get_memory_by_id tool"""
        try:
            await self.validate_arguments(arguments)
            
            memory_id = arguments["memory_id"]
            
            # Call Mem0 API
            response = await self.adapter.get_memory_by_id(memory_id)
            
            # Handle direct API response format (based on direct testing)
            if isinstance(response, dict) and "id" in response:
                # Direct memory object from API
                memory = response
            elif isinstance(response, dict) and "result" in response:
                # Wrapped response
                memory = response["result"]
            else:
                return ToolResult.error(f"Failed to get memory: Unknown error occurred")
                
            if not memory:
                return ToolResult.error(f"Memory with ID {memory_id} not found")
                
            # Format memory details
            result_lines = [f"Memory Details (ID: {memory_id}):"]
            result_lines.append(f"Content: {memory.get('memory', 'N/A')}")
            result_lines.append(f"User ID: {memory.get('user_id', 'N/A')}")
            result_lines.append(f"Created: {memory.get('created_at', 'N/A')}")
            result_lines.append(f"Updated: {memory.get('updated_at', 'N/A')}")
            
            if memory.get("metadata"):
                result_lines.append(f"Metadata: {json.dumps(memory['metadata'], indent=2)}")
            
            content = [
                {"type": "text", "text": "\n".join(result_lines)},
                {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
            ]
            return ToolResult(content=content)
                
        except Exception as e:
            self.logger.error(f"Error in get_memory_by_id: {str(e)}")
            return ToolResult.error(str(e))
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for get_memory_by_id"""
        from ..protocol.message_types import GET_MEMORY_BY_ID_SCHEMA
        return GET_MEMORY_BY_ID_SCHEMA


class DeleteMemoryTool(BaseTool):
    """Tool for deleting a specific memory"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="delete_memory",
            description="Delete a specific memory by its ID"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute delete_memory tool"""
        try:
            await self.validate_arguments(arguments)
            
            memory_id = arguments["memory_id"]
            
            # Call Mem0 API
            response = await self.adapter.delete_memory(memory_id)
            
            # Debug: Print response type and content
            self.logger.debug(f"delete_memory response type: {type(response)}")
            self.logger.debug(f"delete_memory response: {response}")
            
            # Handle different response formats
            if isinstance(response, dict):
                if response.get("success"):
                    return ToolResult.success(f"Successfully deleted memory with ID: {memory_id}")
                else:
                    error_msg = response.get("error", "Unknown error occurred")
                    return ToolResult.error(f"Failed to delete memory: {error_msg}")
            elif isinstance(response, bool) and response:
                # Some APIs might return just a boolean
                return ToolResult.success(f"Successfully deleted memory with ID: {memory_id}")
            else:
                return ToolResult.error(f"Unexpected response format: {type(response)}")
                
        except Exception as e:
            self.logger.error(f"Error in delete_memory: {str(e)}")
            return ToolResult.error(str(e))
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for delete_memory"""
        from ..protocol.message_types import DELETE_MEMORY_SCHEMA
        return DELETE_MEMORY_SCHEMA


class BatchDeleteMemoriesTool(BaseTool):
    """Tool for batch deleting memories"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="batch_delete_memories",
            description="Delete all memories for a user, agent, or run"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute batch_delete_memories tool"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            # Add any additional arguments
            for key, value in arguments.items():
                if key not in ["user_id", "agent_id", "run_id"] and value is not None:
                    params[key] = value
            
            self.logger.debug(f"Batch deleting memories for identity: {identity.get_primary_id()}")
            
            # Call Mem0 API
            response = await self.adapter.batch_delete_memories(**params)
            
            # Debug: Print response type and content
            self.logger.debug(f"batch_delete_memories response type: {type(response)}")
            self.logger.debug(f"batch_delete_memories response: {response}")
            
            # Handle different response formats
            success = False
            if isinstance(response, dict):
                success = response.get("success", False)
                error_msg = response.get("error", "Unknown error occurred")
            elif isinstance(response, bool):
                success = response
                error_msg = "Unknown error occurred"
            else:
                return ToolResult.error(f"Unexpected response format: {type(response)}")
            
            if success:
                identifier_info = []
                if arguments.get("user_id"):
                    identifier_info.append(f"user_id: {arguments['user_id']}")
                if arguments.get("agent_id"):
                    identifier_info.append(f"agent_id: {arguments['agent_id']}")
                if arguments.get("run_id"):
                    identifier_info.append(f"run_id: {arguments['run_id']}")
                
                return ToolResult.success(
                    f"Successfully deleted all memories for {', '.join(identifier_info)}"
                )
            else:
                return ToolResult.error(f"Failed to batch delete memories: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error in batch_delete_memories: {str(e)}")
            return ToolResult.error(str(e))
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for batch_delete_memories"""
        from ..protocol.message_types import BATCH_DELETE_MEMORIES_SCHEMA
        return BATCH_DELETE_MEMORIES_SCHEMA


class UpdateMemoryTool(BaseTool):
    """Tool for updating existing memories"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="update_memory",
            description="Update an existing memory by ID"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute update_memory tool"""
        try:
            await self.validate_arguments(arguments)
            
            # Extract required parameters
            memory_id = arguments["memory_id"]
            data = arguments["data"]
            
            self.logger.debug(f"Updating memory {memory_id} with new data: {data[:100]}...")
            
            # Call Mem0 API
            response = await self.adapter.update_memory(memory_id=memory_id, data=data)
            
            # Debug: Print response type and content
            self.logger.debug(f"update_memory response type: {type(response)}")
            self.logger.debug(f"update_memory response: {response}")
            
            # Handle different response formats
            success = False
            message = "Memory updated successfully"
            
            if isinstance(response, dict):
                # Check for success indicators
                success = (
                    response.get("success", False) or
                    "message" in response or
                    "memory_id" in response or
                    "updated" in str(response).lower()
                )
                
                if response.get("message"):
                    message = response["message"]
                elif response.get("memory_id"):
                    message = f"Memory {response['memory_id']} updated successfully"
            elif isinstance(response, bool):
                success = response
            else:
                # If we get any response without error, consider it success
                success = True
            
            if success:
                return ToolResult.success(f"Successfully updated memory {memory_id}: {message}")
            else:
                error_msg = response.get("error", "Unknown error occurred") if isinstance(response, dict) else str(response)
                return ToolResult.error(f"Failed to update memory {memory_id}: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error in update_memory: {str(e)}")
            return ToolResult.error(str(e))
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for update_memory"""
        from ..protocol.message_types import UPDATE_MEMORY_SCHEMA
        return UPDATE_MEMORY_SCHEMA


class MemoryToolsExecutor:
    """
    Executor for memory-related MCP tools
    """
    
    def __init__(self, config: MCPConfig):
        self.config = config
        self.client: Optional[Mem0HTTPClient] = None
        self.adapter: Optional[BaseAdapter] = None
        self.tools: Dict[str, BaseTool] = {}
        
        logger.info(f"Initialized MemoryToolsExecutor for {config.mem0_base_url}/{config.mem0_api_version}")
    
    async def initialize(self) -> None:
        """Initialize HTTP client and tools"""
        if self.client is None:
            self.client = Mem0HTTPClient(self.config)
            await self.client.connect()
            
            # Create appropriate adapter
            if self.config.mem0_api_version == "v1":
                self.adapter = V1Adapter(self.client)
            elif self.config.mem0_api_version == "v2":
                self.adapter = V2Adapter(self.client)
            else:
                raise ValueError(f"Unsupported API version: {self.config.mem0_api_version}")
            
            # Initialize tools
            self.tools = {
                # Memory management tools
                "add_memory": AddMemoryTool(self.adapter),
                "search_memories": SearchMemoriesTool(self.adapter),
                "get_memories": GetMemoriesTool(self.adapter),
                "get_memory_by_id": GetMemoryByIdTool(self.adapter),
                "delete_memory": DeleteMemoryTool(self.adapter),
                "update_memory": UpdateMemoryTool(self.adapter),
                "batch_delete_memories": BatchDeleteMemoriesTool(self.adapter),
                
                # Graph database management tools
                "manage_graph_entities": GraphEntityTool(self.adapter),
                "manage_graph_relationships": GraphRelationshipTool(self.adapter),
                
                # Advanced memory management tools
                "selective_memory": SelectiveMemoryTool(self.adapter),
                "criteria_retrieval": CriteriaRetrievalTool(self.adapter)
            }
            
            logger.info(f"Initialized {len(self.tools)} tools (7 memory + 2 graph + 2 advanced)")  
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self.client:
            await self.client.close()
            self.client = None
            self.adapter = None
            self.tools.clear()
            logger.info("Cleaned up MemoryToolsExecutor")
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any], request_id: str) -> JSONRPCResponse:
        """
        Execute a tool and return JSON-RPC response
        
        Args:
            tool_name: Name of tool to execute
            arguments: Tool arguments
            request_id: Request ID for response
            
        Returns:
            JSON-RPC response
        """
        try:
            if tool_name not in self.tools:
                return JSONRPCHandler.create_error_response(
                    -32601,  # Method not found
                    f"Tool '{tool_name}' not found",
                    id=request_id
                )
            
            tool = self.tools[tool_name]
            
            # Ensure initialized
            await self.initialize()
            
            # Execute tool directly
            result = await tool.execute(arguments)
            
            # Convert ToolResult to JSON-RPC response
            response_content = ToolsCallResponse(
                content=result.content,
                isError=result.is_error
            )
            
            return JSONRPCHandler.create_response(
                result=response_content.to_dict(),
                id=request_id
            )
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {str(e)}")
            return JSONRPCHandler.create_error_response(
                -32603,  # Internal error
                f"Error executing tool: {str(e)}",
                id=request_id
            )